import { eq, desc, and, sql, like, or, ilike } from 'drizzle-orm';
import { db, testConnection } from './db';
import {
  petSpecters,
  nftTransactions,
  walletBalances,
  type PetSpecter,
  type InsertPetSpecter,
  type NftTransaction,
  type InsertNftTransaction,
  type WalletBalance,
  type InsertWalletBalance
} from "@shared/petSchema";
import { MemPetStorage } from './memPetStorage';

export class PetStorage {
  private memStorage: MemPetStorage;
  private useMemStorage: boolean = false;

  constructor() {
    this.memStorage = new MemPetStorage();

    // Test database connection and set storage mode
    this.initializeStorage();
  }

  private async initializeStorage() {
    try {
      const dbConnected = await testConnection();
      this.useMemStorage = !dbConnected;

      if (this.useMemStorage) {
        console.warn('Database connection failed, using in-memory pet storage');
      } else {
        console.log('Using PostgreSQL for pet storage');
      }
    } catch (error) {
      console.error('Error testing database connection:', error);
      this.useMemStorage = true;
      console.warn('Error connecting to database, using in-memory pet storage');
    }
  }
  // Pet Specter methods
  async getPetSpecterById(id: number): Promise<PetSpecter | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpecterById(id);
    }

    try {
      const result = await db.select().from(petSpecters).where(eq(petSpecters.id, id));
      return result[0];
    } catch (error) {
      console.error('Database error in getPetSpecterById:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpecterById(id);
    }
  }

  async getPetSpecterByGameId(gameId: string): Promise<PetSpecter | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpecterByGameId(gameId);
    }

    try {
      const result = await db.select().from(petSpecters).where(eq(petSpecters.gameId, gameId));
      return result[0];
    } catch (error) {
      console.error('Database error in getPetSpecterByGameId:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpecterByGameId(gameId);
    }
  }

  async getPetSpecterByNftId(tokenId: number): Promise<PetSpecter | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpecterByTokenId(tokenId.toString());
    }

    try {
      const result = await db.select().from(petSpecters).where(eq(petSpecters.tokenId, tokenId.toString()));
      return result[0];
    } catch (error) {
      console.error('Database error in getPetSpecterByNftId:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpecterByTokenId(tokenId.toString());
    }
  }

  async getPetSpecterByTokenId(tokenId: string): Promise<PetSpecter | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpecterByTokenId(tokenId);
    }

    try {
      const result = await db.select().from(petSpecters).where(eq(petSpecters.tokenId, tokenId));
      return result[0];
    } catch (error) {
      console.error('Database error in getPetSpecterByTokenId:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpecterByTokenId(tokenId);
    }
  }

  async getPetSpectersByWalletAddress(walletAddress: string): Promise<PetSpecter[]> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpectersByWalletAddress(walletAddress);
    }

    try {
      // Use the shared database connection
      const result = await db.select()
        .from(petSpecters)
        .where(eq(petSpecters.walletAddress, walletAddress))
        .orderBy(desc(petSpecters.lastActive));

      return result;
    } catch (error) {
      console.error('Database error in getPetSpectersByWalletAddress:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpectersByWalletAddress(walletAddress);
    }
  }

  async getPetSpectersByOwnerId(ownerId: number): Promise<PetSpecter[]> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpectersByOwnerId(ownerId);
    }

    try {
      return await db.select()
        .from(petSpecters)
        .where(eq(petSpecters.ownerId, ownerId))
        .orderBy(desc(petSpecters.lastActive));
    } catch (error) {
      console.error('Database error in getPetSpectersByOwnerId:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpectersByOwnerId(ownerId);
    }
  }

  async createPetSpecter(petSpecter: InsertPetSpecter): Promise<PetSpecter> {
    if (this.useMemStorage) {
      return this.memStorage.createPetSpecter(petSpecter);
    }

    try {
      const now = new Date();

      const newPetSpecter = {
        ...petSpecter,
        createdAt: now,
        lastActive: now
      };

      // Use the shared database connection
      const result = await db.insert(petSpecters)
        .values(newPetSpecter)
        .returning();

      console.log(`Created pet specter with ID=${result[0].id}, name=${petSpecter.name}, type=${petSpecter.specterType}`);
      return result[0];
    } catch (error) {
      console.error('Database error in createPetSpecter:', error);
      this.useMemStorage = true;
      return this.memStorage.createPetSpecter(petSpecter);
    }
  }

  async updatePetSpecter(id: number, updates: Partial<PetSpecter>): Promise<PetSpecter | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.updatePetSpecter(id, updates);
    }

    try {
      // Update lastActive timestamp
      updates.lastActive = new Date();

      const result = await db.update(petSpecters)
        .set(updates)
        .where(eq(petSpecters.id, id))
        .returning();

      return result[0];
    } catch (error) {
      console.error('Database error in updatePetSpecter:', error);
      this.useMemStorage = true;
      return this.memStorage.updatePetSpecter(id, updates);
    }
  }

  async linkPetSpecterToNFT(gameId: string, tokenId: string, walletAddress: string): Promise<PetSpecter | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.linkPetSpecterToNFT(gameId, tokenId, walletAddress);
    }

    try {
      const result = await db.update(petSpecters)
        .set({
          tokenId,
          walletAddress,
          lastActive: new Date()
        })
        .where(eq(petSpecters.gameId, gameId))
        .returning();

      return result[0];
    } catch (error) {
      console.error('Database error in linkPetSpecterToNFT:', error);
      this.useMemStorage = true;
      return this.memStorage.linkPetSpecterToNFT(gameId, tokenId, walletAddress);
    }
  }

  async deletePetSpecter(id: number): Promise<boolean> {
    if (this.useMemStorage) {
      return this.memStorage.deletePetSpecter(id);
    }

    try {
      const result = await db.delete(petSpecters)
        .where(eq(petSpecters.id, id))
        .returning();

      return result.length > 0;
    } catch (error) {
      console.error('Database error in deletePetSpecter:', error);
      this.useMemStorage = true;
      return this.memStorage.deletePetSpecter(id);
    }
  }

  // NFT Transaction methods
  async getNftTransactionByHash(txHash: string): Promise<NftTransaction | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.getNftTransactionByHash(txHash);
    }

    try {
      const result = await db.select().from(nftTransactions).where(eq(nftTransactions.txHash, txHash));
      return result[0];
    } catch (error) {
      console.error('Database error in getNftTransactionByHash:', error);
      this.useMemStorage = true;
      return this.memStorage.getNftTransactionByHash(txHash);
    }
  }

  async getNftTransactionsByTokenId(tokenId: string): Promise<NftTransaction[]> {
    if (this.useMemStorage) {
      return this.memStorage.getNftTransactionsByTokenId(tokenId);
    }

    try {
      return await db.select()
        .from(nftTransactions)
        .where(eq(nftTransactions.tokenId, tokenId))
        .orderBy(desc(nftTransactions.timestamp));
    } catch (error) {
      console.error('Database error in getNftTransactionsByTokenId:', error);
      this.useMemStorage = true;
      return this.memStorage.getNftTransactionsByTokenId(tokenId);
    }
  }

  async getNftTransactionsByWalletAddress(walletAddress: string): Promise<NftTransaction[]> {
    if (this.useMemStorage) {
      return this.memStorage.getNftTransactionsByWalletAddress(walletAddress);
    }

    try {
      return await db.select()
        .from(nftTransactions)
        .where(eq(nftTransactions.walletAddress, walletAddress))
        .orderBy(desc(nftTransactions.timestamp));
    } catch (error) {
      console.error('Database error in getNftTransactionsByWalletAddress:', error);
      this.useMemStorage = true;
      return this.memStorage.getNftTransactionsByWalletAddress(walletAddress);
    }
  }

  async createNftTransaction(transaction: InsertNftTransaction): Promise<NftTransaction> {
    if (this.useMemStorage) {
      return this.memStorage.createNftTransaction(transaction);
    }

    try {
      const result = await db.insert(nftTransactions)
        .values({
          ...transaction,
          timestamp: new Date()
        })
        .returning();

      console.log(`Created NFT transaction with hash=${transaction.txHash}, tokenId=${transaction.tokenId}`);
      return result[0];
    } catch (error) {
      console.error('Database error in createNftTransaction:', error);
      this.useMemStorage = true;
      return this.memStorage.createNftTransaction(transaction);
    }
  }

  async updateNftTransactionStatus(txHash: string, status: string): Promise<NftTransaction | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.updateNftTransactionStatus(txHash, status);
    }

    try {
      const result = await db.update(nftTransactions)
        .set({ status })
        .where(eq(nftTransactions.txHash, txHash))
        .returning();

      return result[0];
    } catch (error) {
      console.error('Database error in updateNftTransactionStatus:', error);
      this.useMemStorage = true;
      return this.memStorage.updateNftTransactionStatus(txHash, status);
    }
  }

  // Wallet Balance methods
  async getWalletBalance(walletAddress: string): Promise<WalletBalance | undefined> {
    if (this.useMemStorage) {
      return this.memStorage.getWalletBalance(walletAddress);
    }

    try {
      const result = await db.select().from(walletBalances).where(eq(walletBalances.walletAddress, walletAddress));
      return result[0];
    } catch (error) {
      console.error('Database error in getWalletBalance:', error);
      this.useMemStorage = true;
      return this.memStorage.getWalletBalance(walletAddress);
    }
  }

  async createOrUpdateWalletBalance(balance: InsertWalletBalance): Promise<WalletBalance> {
    if (this.useMemStorage) {
      return this.memStorage.createOrUpdateWalletBalance(balance);
    }

    try {
      // Check if wallet exists
      const existing = await this.getWalletBalance(balance.walletAddress);

      if (existing) {
        // Update existing wallet
        const result = await db.update(walletBalances)
          .set({
            ...balance,
            lastUpdated: new Date()
          })
          .where(eq(walletBalances.walletAddress, balance.walletAddress))
          .returning();

        return result[0];
      } else {
        // Create new wallet balance
        const result = await db.insert(walletBalances)
          .values({
            ...balance,
            lastUpdated: new Date()
          })
          .returning();

        return result[0];
      }
    } catch (error) {
      console.error('Database error in createOrUpdateWalletBalance:', error);
      this.useMemStorage = true;
      return this.memStorage.createOrUpdateWalletBalance(balance);
    }
  }

  // Methods for finding pet specters by metadata
  async getPetSpectersByMetadataOrangeID(orangeID: string): Promise<PetSpecter[]> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpectersByMetadataOrangeID ?
        this.memStorage.getPetSpectersByMetadataOrangeID(orangeID) :
        [];
    }

    try {
      // Use the shared database connection
      // Execute the query - look for orangeID in metadata
      // This uses a JSON path query to find pets where metadata contains orangeIDUser.id = orangeID
      const result = await db.select()
        .from(petSpecters)
        .where(
          or(
            sql`${petSpecters.metadata}::jsonb @> '{"orangeIDUser": {"id": "${orangeID}"}}'::jsonb`,
            sql`${petSpecters.metadata}::jsonb @> '{"orangeID": "${orangeID}"}'::jsonb`
          )
        )
        .orderBy(desc(petSpecters.lastActive));

      return result;
    } catch (error) {
      console.error('Database error in getPetSpectersByMetadataOrangeID:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpectersByMetadataOrangeID ?
        this.memStorage.getPetSpectersByMetadataOrangeID(orangeID) :
        [];
    }
  }

  async getPetSpectersByMetadataWalletAddress(walletAddress: string): Promise<PetSpecter[]> {
    if (this.useMemStorage) {
      return this.memStorage.getPetSpectersByMetadataWalletAddress ?
        this.memStorage.getPetSpectersByMetadataWalletAddress(walletAddress) :
        [];
    }

    try {
      // Use the shared database connection
      // Execute the query - look for walletAddress in metadata
      // This uses a JSON path query to find pets where metadata contains ethAddress = walletAddress
      const result = await db.select()
        .from(petSpecters)
        .where(
          or(
            sql`${petSpecters.metadata}::jsonb @> '{"ethAddress": "${walletAddress}"}'::jsonb`,
            sql`${petSpecters.metadata}::jsonb @> '{"orangeIDUser": {"ethAddress": "${walletAddress}"}}'::jsonb`
          )
        )
        .orderBy(desc(petSpecters.lastActive));

      return result;
    } catch (error) {
      console.error('Database error in getPetSpectersByMetadataWalletAddress:', error);
      this.useMemStorage = true;
      return this.memStorage.getPetSpectersByMetadataWalletAddress ?
        this.memStorage.getPetSpectersByMetadataWalletAddress(walletAddress) :
        [];
    }
  }
}
