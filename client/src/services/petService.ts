import { useWeb3 } from '@/contexts/Web3Context';
import { SpecterType } from '@/game/types';
import { PetSpecter } from '@/game/entities/PetSpecter';

// Define types for API responses
export interface ApiPetSpecter {
  id: number;
  gameId: string;
  name: string;
  ownerId?: number;
  walletAddress?: string;
  tokenId?: string;
  specterType: string;
  level: number;
  xp: number;
  traits: any[];
  equipment: any;
  stats: any;
  createdAt: string;
  lastActive: string;
  metadata: any;
}

interface ApiNftTransaction {
  id: number;
  txHash: string;
  tokenId: string;
  walletAddress: string;
  petSpecterId?: number;
  price: string;
  platformFee: string;
  timestamp: string;
  status: string;
  metadata: any;
}

// Base API URL
const API_BASE_URL = '/api/pets';

/**
 * Service for interacting with pet specters and NFTs
 */
export class PetService {
  // Cache for pet specters to avoid repeated API calls
  private static petSpecterCache: Map<string, ApiPetSpecter[]> = new Map();

  // Flag to track if auth is ready
  private static isAuthReady: boolean = false;

  // Flag to track if we've registered auth listeners
  private static hasRegisteredAuthListeners: boolean = false;

  // Storage key for local pet data
  private static readonly LOCAL_PETS_KEY = 'local_pet_specters';

  /**
   * Get current user identifier with improved fallback mechanisms
   */
  static getCurrentUserIdentifier(): string | null {
    let userIdentifier = null;
    let source = null;

    // First try to get from auth context (preferred method)
    if (typeof window !== 'undefined' && window.authContext) {
      if (window.authContext.walletAddress) {
        userIdentifier = window.authContext.walletAddress;
        source = 'authContext.walletAddress';
      } else if (window.authContext.orangeIDUser && window.authContext.orangeIDUser.id) {
        userIdentifier = window.authContext.orangeIDUser.id;
        source = 'authContext.orangeIDUser.id';
      }
    }

    // If not found in auth context, try localStorage as fallback
    if (!userIdentifier) {
      // Try getting wallet from localStorage
      const storedWallet = localStorage.getItem('walletAddress');
      if (storedWallet && storedWallet !== 'undefined' && storedWallet !== 'null') {
        userIdentifier = storedWallet;
        source = 'localStorage.walletAddress';
      } else {
        // Try getting orangeID from localStorage
        const orangeIDUserStr = localStorage.getItem('orangeIDUser');
        if (orangeIDUserStr && orangeIDUserStr !== 'undefined' && orangeIDUserStr !== 'null') {
          try {
            const orangeIDUser = JSON.parse(orangeIDUserStr);
            if (orangeIDUser && orangeIDUser.id) {
              userIdentifier = orangeIDUser.id;
              source = 'localStorage.orangeIDUser.id';
            } else if (orangeIDUser && orangeIDUser.ethAddress) {
              userIdentifier = orangeIDUser.ethAddress;
              source = 'localStorage.orangeIDUser.ethAddress';
            }
          } catch (e) {
            console.error('Error parsing orangeIDUser from localStorage:', e);
          }
        }
      }
    }

    // If still no identifier, try sessionStorage as last resort
    if (!userIdentifier && typeof window !== 'undefined') {
      const sessionWallet = sessionStorage.getItem('tempWalletAddress');
      if (sessionWallet && sessionWallet !== 'undefined' && sessionWallet !== 'null') {
        userIdentifier = sessionWallet;
        source = 'sessionStorage.tempWalletAddress';
      }
    }

    // Log the result for debugging
    if (userIdentifier) {
      console.log(`[PetService] User identified as: ${userIdentifier} (source: ${source})`);

      // Store in localStorage for future use
      if (source && !source.includes('localStorage')) {
        localStorage.setItem('walletAddress', userIdentifier);
      }
    } else {
      console.warn('[PetService] No authenticated user found in auth context or local storage');

      // Register auth event listeners if not already done
      if (!this.hasRegisteredAuthListeners && typeof window !== 'undefined') {
        window.addEventListener('auth-login-success', () => {
          console.log('[PetService] Auth login success event detected, auth should be ready now');
          this.isAuthReady = true;
          // Clear cache to force refresh
          this.petSpecterCache.clear();
        });

        this.hasRegisteredAuthListeners = true;
      }
    }

    return userIdentifier;
  }

  /**
   * Wait for auth to be ready before proceeding
   * Returns true if auth became ready, false if timed out
   */
  static async waitForAuth(maxWaitMs: number = 5000): Promise<boolean> {
    // If auth is already ready, return immediately
    if (this.isAuthReady || this.getCurrentUserIdentifier()) {
      return true;
    }

    return new Promise((resolve) => {
      // Set a timeout to avoid waiting forever
      const timeoutId = setTimeout(() => {
        console.warn('[PetService] Timed out waiting for auth to be ready');
        resolve(false);
      }, maxWaitMs);

      // Function to check auth status periodically
      const checkAuth = () => {
        if (this.getCurrentUserIdentifier()) {
          clearTimeout(timeoutId);
          this.isAuthReady = true;
          resolve(true);
          return;
        }

        // Check again in 100ms
        setTimeout(checkAuth, 100);
      };

      // Start checking
      checkAuth();

      // Also listen for auth events
      const authSuccessHandler = () => {
        clearTimeout(timeoutId);
        this.isAuthReady = true;
        resolve(true);
        window.removeEventListener('auth-login-success', authSuccessHandler);
      };

      window.addEventListener('auth-login-success', authSuccessHandler);
    });
  }

  /**
   * Get all pet specters for a user (wallet address or OrangeID)
   */
  static async getPetSpectersByWallet(userIdentifier: string): Promise<ApiPetSpecter[]> {
    try {
      // Check cache first
      if (this.petSpecterCache.has(userIdentifier)) {
        console.log(`[PetService] Using cached pet specters for user: ${userIdentifier}`);
        return this.petSpecterCache.get(userIdentifier) || [];
      }

      console.log(`[PetService] Fetching pet specters for user: ${userIdentifier}`);
      const response = await fetch(`${API_BASE_URL}/wallet/${userIdentifier}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch pet specters: ${response.statusText}`);
      }

      const pets = await response.json();
      console.log(`[PetService] Found ${pets.length} pet specters for user ${userIdentifier}`);

      // Cache the results
      this.petSpecterCache.set(userIdentifier, pets);

      // Also store in localStorage as backup
      this.saveLocalPetSpecters(userIdentifier, pets);

      return pets;
    } catch (error) {
      console.error('[PetService] Error fetching pet specters:', error);

      // Try to get from localStorage as fallback
      const localPets = this.getLocalPetSpecters(userIdentifier);
      if (localPets && localPets.length > 0) {
        console.log(`[PetService] Using ${localPets.length} pet specters from local storage for user ${userIdentifier}`);
        return localPets;
      }

      throw error;
    }
  }

  /**
   * Save pet specters to localStorage as a backup
   */
  private static saveLocalPetSpecters(userIdentifier: string, pets: ApiPetSpecter[]): void {
    try {
      const localPetsMap = this.getLocalPetsMap();
      localPetsMap[userIdentifier] = pets;
      localStorage.setItem(this.LOCAL_PETS_KEY, JSON.stringify(localPetsMap));
    } catch (error) {
      console.error('[PetService] Error saving pet specters to localStorage:', error);
    }
  }

  /**
   * Get pet specters from localStorage
   */
  private static getLocalPetSpecters(userIdentifier: string): ApiPetSpecter[] {
    try {
      const localPetsMap = this.getLocalPetsMap();
      return localPetsMap[userIdentifier] || [];
    } catch (error) {
      console.error('[PetService] Error getting pet specters from localStorage:', error);
      return [];
    }
  }

  /**
   * Get the local pets map from localStorage
   */
  private static getLocalPetsMap(): Record<string, ApiPetSpecter[]> {
    try {
      const localPetsStr = localStorage.getItem(this.LOCAL_PETS_KEY);
      if (!localPetsStr) return {};
      return JSON.parse(localPetsStr);
    } catch (error) {
      console.error('[PetService] Error parsing local pets map:', error);
      return {};
    }
  }

  /**
   * Get all pet specters for the current user with retry logic
   */
  static async getCurrentUserPetSpecters(retryCount: number = 3): Promise<ApiPetSpecter[]> {
    const userIdentifier = this.getCurrentUserIdentifier();

    if (userIdentifier) {
      return this.getPetSpectersByWallet(userIdentifier);
    }

    // If no user identifier yet, wait for auth and retry
    if (retryCount > 0) {
      console.log(`[PetService] No user identifier yet, waiting for auth (${retryCount} retries left)`);
      const authReady = await this.waitForAuth(2000);

      if (authReady) {
        const newIdentifier = this.getCurrentUserIdentifier();
        if (newIdentifier) {
          return this.getPetSpectersByWallet(newIdentifier);
        }
      }

      // Retry with decremented count
      return this.getCurrentUserPetSpecters(retryCount - 1);
    }

    console.warn('[PetService] Could not get user identifier after retries, returning empty pet list');
    return [];
  }

  /**
   * Check if the current user has an Orange Pet Specter
   */
  static async hasOrangePetSpecter(): Promise<boolean> {
    try {
      const pets = await this.getCurrentUserPetSpecters();
      return pets.some(pet =>
        pet.specterType.toLowerCase() === 'orange' ||
        pet.specterType.toLowerCase() === 'orangepet'
      );
    } catch (error) {
      console.error('[PetService] Error checking for Orange Pet Specter:', error);
      return false;
    }
  }

  /**
   * Get a pet specter by token ID
   */
  static async getPetSpecterByTokenId(tokenId: string, walletAddress: string): Promise<ApiPetSpecter> {
    try {
      const response = await fetch(`${API_BASE_URL}/nft/${tokenId}?walletAddress=${walletAddress}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch pet specter: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching pet specter:', error);
      throw error;
    }
  }

  /**
   * Get a pet specter by game ID
   */
  static async getPetSpecterByGameId(gameId: string): Promise<ApiPetSpecter> {
    try {
      const response = await fetch(`${API_BASE_URL}/game-id/${gameId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch pet specter by game ID: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching pet specter by game ID:', error);
      throw error;
    }
  }

  /**
   * Create a new pet specter
   */
  static async createPetSpecter(petSpecter: {
    gameId: string;
    name: string;
    walletAddress?: string;
    tokenId?: string;
    specterType: string;
    level?: number;
    xp?: number;
    traits?: any[];
    equipment?: any;
    stats?: any;
    metadata?: any;
  }): Promise<ApiPetSpecter> {
    try {
      // If no wallet address is provided, try to get the current user's identifier
      if (!petSpecter.walletAddress) {
        const userIdentifier = this.getCurrentUserIdentifier();
        if (userIdentifier) {
          petSpecter.walletAddress = userIdentifier;
        }
      }

      const response = await fetch(`${API_BASE_URL}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(petSpecter),
      });

      if (!response.ok) {
        throw new Error(`Failed to create pet specter: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating pet specter:', error);
      throw error;
    }
  }

  /**
   * Update a pet specter
   */
  static async updatePetSpecter(id: number, updates: Partial<ApiPetSpecter>): Promise<ApiPetSpecter> {
    try {
      const response = await fetch(`${API_BASE_URL}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(`Failed to update pet specter: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating pet specter:', error);
      throw error;
    }
  }

  /**
   * Update a pet specter by game ID
   */
  static async updatePetSpecterByGameId(gameId: string, updates: Partial<ApiPetSpecter>): Promise<ApiPetSpecter> {
    try {
      // First get the pet by game ID to find its database ID
      const pet = await this.getPetSpecterByGameId(gameId);
      if (!pet) {
        throw new Error(`Pet specter with game ID ${gameId} not found`);
      }

      // Now update using the database ID
      return await this.updatePetSpecter(pet.id, updates);
    } catch (error) {
      console.error(`Error updating pet specter with game ID ${gameId}:`, error);
      throw error;
    }
  }

  /**
   * Link a pet specter to an NFT
   */
  static async linkPetSpecterToNFT(gameId: string, tokenId: string, walletAddress: string): Promise<ApiPetSpecter> {
    try {
      console.log(`Linking pet specter ${gameId} to NFT ${tokenId} for wallet ${walletAddress}`);

      // First, check if the pet exists in the database
      try {
        // Try with the exact gameId first
        let checkResponse = await fetch(`${API_BASE_URL}/game-id/${gameId}`);

        // If not found, try with a different format (some might be stored with 'pet-' prefix, some without)
        if (!checkResponse.ok) {
          console.warn(`Pet specter with gameId ${gameId} not found, trying alternative format...`);

          // If gameId starts with 'pet-', try without it
          if (gameId.startsWith('pet-')) {
            const numericId = gameId.replace('pet-', '');
            checkResponse = await fetch(`${API_BASE_URL}/game-id/${numericId}`);
          }
          // Otherwise, try with 'pet-' prefix
          else {
            const prefixedId = `pet-${gameId}`;
            checkResponse = await fetch(`${API_BASE_URL}/game-id/${prefixedId}`);
          }

          // If still not found, use mock in development/test mode
          if (!checkResponse.ok) {
            console.warn(`Pet specter still not found with alternative format, may need to retry later`);
            // If we're in development mode, we can create a mock response
            if (process.env.NODE_ENV === 'development') {
              console.log('Development mode: Creating mock pet specter link response');
              return {
                id: 1,
                gameId,
                name: 'Mock Pet',
                walletAddress,
                tokenId,
                specterType: 'WISP',
                level: 1,
                xp: 0,
                traits: [],
                equipment: {},
                stats: {},
                createdAt: new Date().toISOString(),
                lastActive: new Date().toISOString(),
                metadata: { imageUrl: localStorage.getItem(`nft_pet_image_${tokenId}`) || localStorage.getItem(`pet_image_${gameId}`) || undefined }
              };
            }
          }
        }
      } catch (checkError) {
        console.warn('Error checking pet specter existence:', checkError);
      }

      // Attempt to link the pet to the NFT
      const response = await fetch(`${API_BASE_URL}/link-nft`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gameId, tokenId, walletAddress }),
      });

      if (!response.ok) {
        console.error(`Failed to link pet specter to NFT: ${response.statusText}`);

        // If we're in development mode, we can create a mock response
        if (process.env.NODE_ENV === 'development') {
          console.log('Development mode: Creating mock pet specter link response after API failure');
          return {
            id: 1,
            gameId,
            name: 'Mock Pet',
            walletAddress,
            tokenId,
            specterType: 'WISP',
            level: 1,
            xp: 0,
            traits: [],
            equipment: {},
            stats: {},
            createdAt: new Date().toISOString(),
            lastActive: new Date().toISOString(),
            metadata: { imageUrl: localStorage.getItem(`nft_pet_image_${tokenId}`) || localStorage.getItem(`pet_image_${gameId}`) || undefined }
          };
        }

        throw new Error(`Failed to link pet specter to NFT: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Successfully linked pet specter to NFT:', result);
      return result;
    } catch (error) {
      console.error('Error linking pet specter to NFT:', error);
      throw error;
    }
  }

  /**
   * Record an NFT transaction
   */
  static async recordNftTransaction(transaction: {
    txHash: string;
    tokenId: string;
    walletAddress: string;
    petSpecterId?: number;
    price: string;
    platformFee: string;
    status?: string;
    metadata?: any;
  }): Promise<ApiNftTransaction> {
    try {
      // If no wallet address is provided, try to get the current user's identifier
      if (!transaction.walletAddress) {
        const userIdentifier = this.getCurrentUserIdentifier();
        if (userIdentifier) {
          transaction.walletAddress = userIdentifier;
        }
      }

      const response = await fetch(`${API_BASE_URL}/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transaction),
      });

      if (!response.ok) {
        throw new Error(`Failed to record NFT transaction: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error recording NFT transaction:', error);
      throw error;
    }
  }

  /**
   * Get NFT transactions for a wallet
   */
  static async getNftTransactionsByWallet(walletAddress: string): Promise<ApiNftTransaction[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/transactions/wallet/${walletAddress}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch NFT transactions: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching NFT transactions:', error);
      throw error;
    }
  }

  /**
   * Get NFT transactions for the current user
   */
  static async getCurrentUserNftTransactions(): Promise<ApiNftTransaction[]> {
    const userIdentifier = this.getCurrentUserIdentifier();

    if (!userIdentifier) {
      console.warn('No user identifier found, cannot fetch NFT transactions');
      return [];
    }

    return this.getNftTransactionsByWallet(userIdentifier);
  }

  /**
   * Convert a game PetSpecter to an API PetSpecter for storage
   */
  static convertGamePetToApiPet(pet: PetSpecter, walletAddress?: string, tokenId?: string): Omit<ApiPetSpecter, 'id' | 'createdAt' | 'lastActive'> {
    // If no wallet address is provided, try to get the current user's identifier
    if (!walletAddress) {
      const userIdentifier = this.getCurrentUserIdentifier();
      if (userIdentifier) {
        walletAddress = userIdentifier;
      }
    }

    return {
      gameId: pet.id,
      name: pet.name,
      walletAddress,
      tokenId,
      specterType: pet.specterType.name,
      level: pet.level,
      xp: pet.xp,
      traits: pet.traits.map(trait => ({
        type: trait.type,
        level: trait.level,
        xp: trait.xp,
        xpToNextLevel: trait.xpToNextLevel
      })),
      equipment: {
        weapon: pet.equipment.weapon,
        armor: pet.equipment.armor,
        utility: pet.equipment.utility
      },
      stats: {
        health: pet.health,
        maxHealth: pet.maxHealth,
        attackPower: pet.attackPower,
        defenseValue: pet.defenseValue,
        speed: pet.speed
      },
      metadata: {
        ownerID: pet.ownerID
      }
    };
  }

  /**
   * Deploy a pet specter
   */
  static async deployPetSpecter(gameId: string): Promise<boolean> {
    try {
      const userIdentifier = this.getCurrentUserIdentifier();
      if (!userIdentifier) {
        throw new Error('No user identifier available');
      }

      const response = await fetch(`${API_BASE_URL}/deploy/${gameId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userIdentifier
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to deploy pet specter: ${response.statusText}`);
      }

      const result = await response.json();

      // Clear cache to force refresh
      this.petSpecterCache.clear();

      return result.success;
    } catch (error) {
      console.error('[PetService] Error deploying pet specter:', error);
      return false;
    }
  }

  /**
   * Recall a pet specter
   */
  static async recallPetSpecter(gameId: string): Promise<boolean> {
    try {
      const userIdentifier = this.getCurrentUserIdentifier();
      if (!userIdentifier) {
        throw new Error('No user identifier available');
      }

      const response = await fetch(`${API_BASE_URL}/recall/${gameId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userIdentifier
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to recall pet specter: ${response.statusText}`);
      }

      const result = await response.json();

      // Clear cache to force refresh
      this.petSpecterCache.clear();

      return result.success;
    } catch (error) {
      console.error('[PetService] Error recalling pet specter:', error);
      return false;
    }
  }
}
